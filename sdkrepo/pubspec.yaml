name: example
description: MRZScanner example

publish_to: "none"

version: 1.0.0+1

environment:
  sdk: ">=3.4.3 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8
  get: ^4.6.6
  http: ^1.2.1
  logger: ^2.2.0
  responsive_framework: ^0.2.0
  infinite_scroll_pagination: ^4.0.0
  app_settings: ^5.1.1
  shimmer: ^3.0.0
  flutter_svg: ^2.0.10+1
  image_picker: ^1.1.0
  get_storage: ^2.1.1
  intl: ^0.19.0
  photo_view: ^0.14.0
  cached_network_image: ^3.3.1
  connectivity_plus: ^6.0.2
  share_plus: ^8.0.3
  flutter_html: ^3.0.0-beta.2
  encrypt: ^5.0.3
  lottie: ^3.1.0
  flutter_cache_manager: ^3.3.1
  flutter_form_builder: ^9.2.1
  enefty_icons: ^1.0.7
  form_field_validator: ^1.1.0
  #  carousel_slider: ^4.2.1
  #  flutter_config: ^2.0.2
  flutter_spinkit: ^5.2.0
  firebase_core: ^2.24.2
  smooth_page_indicator: ^1.1.0
  slide_countdown: ^1.5.0
  dropdown_search: ^5.0.6
  pinput: ^5.0.0
  panara_dialogs: ^0.1.4
  flutter_native_splash: ^2.3.8
  dotted_border: ^2.1.0
  path: ^1.9.0
  signature: ^5.4.1
  dropdown_button2: ^2.3.9
  intl_phone_field: ^3.2.0
  permission_handler: ^11.3.1
  flutter_image_compress: ^2.2.0
  tripledes_nullsafety:
  mrz_parser:
  google_mlkit_text_recognition:
  shared_preferences: ^2.5.2

  mrz_scanner:
    git:
      url: https://${GITHUB_TOKEN}@github.com/iQLabs-co/documentsreader.git
  face_camera:
    git:
      url: https://${GITHUB_TOKEN}@github.com/iQLabs-co/livenesslibrary.git
  google_maps_place_picker_mb: ^3.1.2
  google_maps_flutter: ^2.5.0
  camerawesome: ^2.0.1
  image: ^4.2.0
  digital_onboarding:
    path: ..

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^2.0.0
  flutter_launcher_icons: ^0.13.1
  rename_app: ^1.3.1

flutter_launcher_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  image_path: assets/app_logo/splash_logo.png
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/app_logo/launcher_icons.png"

flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/lottie/
    - assets/app_logo/
    - assets/images/

  fonts:
    - family: Gotham
      fonts:
        - asset: assets/fonts/Gotham/Gotham-Bold.otf
          weight: 300
        - asset: assets/fonts/Gotham/Gotham-Medium.ttf
          weight: 500
        - asset: assets/fonts/Gotham/Gotham-Light.otf
          weight: 700

    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins/Poppins-Thin.ttf
          weight: 100
        - asset: assets/fonts/Poppins/Poppins-ThinItalic.ttf
          weight: 100
          style: italic
        - asset: assets/fonts/Poppins/Poppins-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/Poppins/Poppins-ExtraLightItalic.ttf
          weight: 200
        - asset: assets/fonts/Poppins/Poppins-Light.ttf
          weight: 300
        - asset: assets/fonts/Poppins/Poppins-LightItalic.ttf
          weight: 300
          style: italic
        - asset: assets/fonts/Poppins/Poppins-Regular.ttf
          weight: 400
        - asset: assets/fonts/Poppins/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins/Poppins-MediumItalic.ttf
          weight: 500
          style: italic
        - asset: assets/fonts/Poppins/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins/Poppins-SemiBoldItalic.ttf
          weight: 600
          style: italic
        - asset: assets/fonts/Poppins/Poppins-Bold.ttf
          weight: 700
        - asset: assets/fonts/Poppins/Poppins-BoldItalic.ttf
          weight: 700
          style: italic
        - asset: assets/fonts/Poppins/Poppins-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/Poppins/Poppins-ExtraBoldItalic.ttf
          weight: 800
          style: italic
        - asset: assets/fonts/Poppins/Poppins-Black.ttf
          weight: 900
        - asset: assets/fonts/Poppins/Poppins-BlackItalic.ttf
          weight: 900
          style: italic
        - asset: assets/fonts/Poppins/Poppins-Italic.ttf
          style: italic

flutter_native_splash:
  image: assets/app_logo/splash_logo.png
  color: "#FFFFFF"
  color_dark: "#FFFFFF"

  android_12:
    color: "#FFFFFF"
    color_dark: "#FFFFFF"
    image: assets/app_logo/splash_logo.png

  ios: true
