
import 'package:flutter_nfc_kit/flutter_nfc_kit.dart';
import 'package:mrz_scanner/mrz_scanner.dart';

// import 'package:mrz_scanner/mrz_scanner.dart';
import '../../resources/exports/index.dart';

class NFCScreen extends GetView<NFCController> {
  const NFCScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final MRZController mrzCtrl = MRZController();
    return MRZScanner(
      controller: mrzCtrl,
      onMessage: (textData) async {
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text(textData)));
      },
      onMrz: (mrzResult) async {
        await showDialog(
          context: context,
          builder: (context) => Dialog(
            insetPadding: const EdgeInsets.symmetric(horizontal: 10),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextButton(
                    onPressed: () async {
                      Navigator.pop(context);
                      mrzCtrl.currentState?.scanning();

                    },
                    child: const Text('Scan Card'),
                  ),
                  Text('Name : ${mrzResult.givenNames}'),
                  Text('Gender : ${mrzResult.sex.name}'),
                  Text('CountryCode : ${mrzResult.countryCode}'),
                  Text('Date of Birth : ${mrzResult.birthDate}'),
                  Text('Expiry Date : ${mrzResult.expiryDate}'),
                  Text('DocNum : ${mrzResult.documentNumber}'),
                ],
              ),
            ),
          ),
        );
      },
      onScan: (mrzData, imagedata) async {
        if (mrzData != null) {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
              content: Text("Data Readed Successfully Uploading Now...")));
          await controller.onVerify(mrzData, imagedata);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text("NFC Data Empty")),
          );
        }
      },
    );
  }
}
