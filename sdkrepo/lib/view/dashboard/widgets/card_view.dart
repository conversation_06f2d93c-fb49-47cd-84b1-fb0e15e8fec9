import 'package:example/resources/exports/index.dart';
import 'package:flutter/cupertino.dart';

class CardView extends StatelessWidget {
  const CardView({
    super.key,
    required this.iconPath,
    required this.title,
    required this.isCompleted,
    this.onPress,
  });

  final String iconPath;
  final String title;
  final bool isCompleted;
  final VoidCallback? onPress;

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      duration: const Duration(milliseconds: 300),
      opacity: onPress == null ? 0.5 : 1.0,
      child: CupertinoButton(
        onPressed: onPress,
        padding: EdgeInsets.zero,
        minSize: 0,
        child: Container(
          width: double.maxFinite,
          padding: const EdgeInsets.all(20.0),
          clipBehavior: Clip.antiAlias,
          decoration: ShapeDecoration(
            color: const Color(0xffE9EFF1),
            shape: ContinuousRectangleBorder(
              borderRadius: BorderRadius.circular(32.0),
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SvgPicture.asset(
                    iconPath,
                    height: 40,
                  ),
                  AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    transitionBuilder: (Widget child, Animation<double> animation) {
                      return ScaleTransition(
                        scale: animation,
                        child: FadeTransition(
                          opacity: animation,
                          child: child,
                        ),
                      );
                    },
                    child: isCompleted
                        ? SvgPicture.asset(
                            "assets/images/check.svg",
                            height: 28,
                          )
                        : const SizedBox(),
                  ),
                ],
              ),
              const SpaceH16(),
              Row(
                children: [
                  Expanded(
                    child: DefaultTextStyle(
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Color(0xff171C1E),
                      ),
                      child: Text(
                        title,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(8.0),
                    decoration: const BoxDecoration(
                      color: Colors.black12,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.arrow_forward_ios_rounded,
                      size: 16,
                      color: Color(0xff3D245B),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
