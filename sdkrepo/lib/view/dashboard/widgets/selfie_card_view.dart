import 'package:example/resources/exports/index.dart';
import 'package:flutter/cupertino.dart';

class SelfieCardView extends StatelessWidget {
  const SelfieCardView({
    super.key,
    required this.isCompleted,
    this.onPress,
  });

  final bool isCompleted;
  final VoidCallback? onPress;

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      duration: const Duration(milliseconds: 300),
      opacity: onPress == null ? 0.5 : 1.0,
      child: Container(
        clipBehavior: Clip.antiAlias,
        decoration: ShapeDecoration(
          gradient: const LinearGradient(
            end: Alignment.topLeft,
            begin: Alignment.bottomRight,
            colors: [Color(0xffD7C0FB), Color(0xffEEE3FF)],
          ),
          shape: ContinuousRectangleBorder(
            borderRadius: BorderRadius.circular(38),
          ),
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                const SizedBox(width: 32),
                SvgPicture.asset(
                  "assets/images/Serfle.svg",
                  height: 100,
                ),
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  transitionBuilder: (Widget child, Animation<double> animation) {
                    return FadeTransition(opacity: animation, child: child);
                  },
                  child: isCompleted
                      ? SvgPicture.asset(
                          "assets/images/check.svg",
                          height: 24,
                        )
                      : const SizedBox(width: 32),
                ),
              ],
            ),
            const SpaceH16(),
            CupertinoButton(
              onPressed: onPress,
              padding: EdgeInsets.zero,
              minSize: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.32),
                  borderRadius: BorderRadius.circular(100),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const DefaultTextStyle(
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        color: AppColors.black,
                      ),
                      child: Text("Selfie verification"),
                    ),
                    Container(
                      padding: const EdgeInsets.all(12.0),
                      decoration: const BoxDecoration(
                        color: Colors.black12,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.arrow_forward_ios_rounded,
                        size: 20,
                        color: Color(0xff3D245B),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
