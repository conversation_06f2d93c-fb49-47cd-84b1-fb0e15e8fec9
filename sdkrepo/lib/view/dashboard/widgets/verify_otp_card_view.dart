import 'package:example/resources/exports/index.dart';
import 'package:flutter/cupertino.dart';

class VerifyOtpCardView extends StatelessWidget {
  const VerifyOtpCardView({
    super.key,
    required this.isCompleted,
    this.onPress,
  });

  final bool isCompleted;
  final VoidCallback? onPress;

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: onPress == null ? 0.5 : 1.0,
      child: CupertinoButton(
        onPressed: onPress,
        padding: EdgeInsets.zero,
        minSize: 0,
        child: Container(
          clipBehavior: Clip.antiAlias,
          decoration: ShapeDecoration(
            gradient: const LinearGradient(
              end: Alignment.topLeft,
              begin: Alignment.bottomRight,
              colors: [
                Color(0xff6C22D1),
                Color(0xff462378),
              ],
            ),
            shape: ContinuousRectangleBorder(
              borderRadius: BorderRadius.circular(38),
            ),
          ),
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Image.asset("assets/images/verify_email_illustration.png", height: 50),
                  const SpaceW16(),
                  const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      DefaultTextStyle(
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color: AppColors.white,
                        ),
                        child: Text("Verify by Email/OTP"),
                      ),
                      SpaceH4(),
                      DefaultTextStyle(
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: AppColors.white,
                        ),
                        child: Text("Authentication process"),
                      ),
                    ],
                  ),
                ],
              ),
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return FadeTransition(opacity: animation, child: child);
                },
                child: isCompleted == true
                    ? SvgPicture.asset(
                        "assets/images/check.svg",
                        height: 38,
                      )
                    : Container(
                        padding: const EdgeInsets.all(9.0),
                        decoration: const BoxDecoration(
                          color: Colors.white12,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          // Icons.arrow_forward_ios_rounded,
                          CupertinoIcons.forward,
                          size: 20,
                          color: Colors.white,
                        ),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
