import '../../../resources/exports/index.dart';

class LoginMobileForm extends GetView<LoginController> {
  const LoginMobileForm({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SpaceH20(),
            Text(Strings.PLEASE_ENTER_YOUR_PHONE, style: context.headlineSmall),
            const SpaceH20(),
            IntlPhoneField(
              decoration: const InputDecoration(
                labelText: Strings.PHONE,
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(12.0)),
                  borderSide: BorderSide(color: AppColors.black),
                ),
              ),
              autovalidateMode: AutovalidateMode.disabled,
              controller: controller.phoneCtrl,
              keyboardType: TextInputType.number,
              initialCountryCode: 'IQ',
              languageCode: "en",
              onChanged: (phone) {},
              onCountryChanged: (country) => controller.countryCode = country,
              inputFormatters: InputFormat.onlyNumber,
            ),
            const SpaceH20(),
            CustomButton.solid(
              backgroundColor: AppColors.primary,
              textColor: AppColors.white,
              text: Strings.VERIFY,
              onTapAsync: () async => controller.sendOtp(LoginType.phone),
              radius: Sizes.RADIUS_12,
              constraints: const BoxConstraints(minHeight: 55),
            ),
          ],
        ),
      ),
    );
  }
}
