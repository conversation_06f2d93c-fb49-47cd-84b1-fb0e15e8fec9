import '../../../resources/exports/index.dart';

class LoginEmailForm extends GetView<LoginController> {
  const LoginEmailForm({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SpaceH20(),
            Text(Strings.PLEASE_ENTER_YOUR_EMAIL, style: context.headlineSmall),
            const SpaceH20(),
            CustomTextFormField(
              controller: controller.emailCtrl,
              isRequired: true,
              height: Sizes.HEIGHT_20,
              labelText: Strings.EMAIL,
              labelColor: AppColors.black,
              prefixIcon: EneftyIcons.sms_outline,
              prefixIconColor: AppColors.black,
              textColor: AppColors.black,
              cursorColor: AppColors.black,
              errorColor: AppColors.black,
              enableBorderColor: AppColors.black,
              focusBorderColor: AppColors.primary,
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.emailAddress,
              autofillHints: const [AutofillHints.email],
              validator: Validators.emailValidation,
            ),
            const SpaceH20(),
            CustomButton.solid(
              backgroundColor: AppColors.primary,
              textColor: AppColors.white,
              text: Strings.VERIFY,
              onTapAsync: () async => controller.sendOtp(LoginType.email),
              radius: Sizes.RADIUS_12,
              constraints: const BoxConstraints(minHeight: 55),
            ),
          ],
        ),
      ),
    );
  }
}
