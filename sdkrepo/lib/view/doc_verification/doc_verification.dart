import '../../resources/exports/index.dart';

class DocVerification extends GetView<DocVerificationController> {
  const DocVerification({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: Strings.DOCUMENT_VERIFICATION,backallow:true),
      backgroundColor: AppColors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            GetBuilder<DocVerificationController>(
              id: 'doc_picture',
              builder: (_) {
                return controller.image == null
                    ? GestureDetector(
                      onTap: controller.pickImage,
                      child: const Icon(
                        EneftyIcons.camera_bold,
                        color: AppColors.primary,
                        size: 85,
                      ))
                    : ImageService.image(
                        controller.image ??
                            'https://iqid.yocat.net/landing-assets/images/logo-w.png',
                        borderRadius: 20.0,
                        imageHeight: 250,
                        imageWidth: 300,
                      );
              },
            ),
            const SpaceH30(),
            Text(Strings.USE_CAMERA, style: context.titleLarge),
            const SpaceH30(),
            CustomButton.solid(
              backgroundColor: AppColors.primary,
              textColor: AppColors.white,
              text: Strings.VERIFY,
              onTapAsync: () async => controller.verify(),
              radius: Sizes.RADIUS_12,
              constraints: const BoxConstraints(minHeight: 55),
            ).constrainedBox(maxWidth: 340.0),
            const SpaceH20(),
            GetBuilder<DocVerificationController>(
              id: 'doc_picture',
              builder: (_) {
                if (controller.doc?.isBackRequired ?? false) {
                  return Center(
                    child: Text(
                      Strings.PLEASE_PROVIDE_IMAGE,
                      style: context.titleLarge.copyWith(
                        color: AppColors.error,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ).paddingSymmetric(horizontal: 16.0);
                } else {
                  return const SizedBox.shrink();
                }
              },
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: AppColors.primary,
        onPressed: controller.pickImage,
        child: const Icon(
          EneftyIcons.camera_outline,
          color: AppColors.white,
        ),
      ),
    );
  }
}
