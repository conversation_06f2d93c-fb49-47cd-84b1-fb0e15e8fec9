import 'package:flutter_nfc_kit/flutter_nfc_kit.dart';

import '../../resources/exports/index.dart';

class Home extends GetView<HomeController> {
  const Home({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ImageService.image(
              Assets.IQID_KYC,
              imageHeight: 100,
              imageWidth: 100,
              imageColor: AppColors.black,
            ),
            const SpaceH20(),
            Text(Strings.PRESS_CONTINUE, style: context.titleLarge),
            const SpaceH20(),
            CustomButton.solid(
              backgroundColor: AppColors.black,
              textColor: AppColors.white,
              text: Strings.START_KYC,

              onTapAsync: () async => controller.onStartKycTap(),
              radius: Sizes.RADIUS_12,
              constraints: const BoxConstraints(minHeight: 55),
            ),
            const SpaceH20(),
            if (controller.isexist)
              CustomButton.solid(
                backgroundColor: AppColors.black,
                textColor: AppColors.white,
                text: Strings.CONTINUE_OLD_KYC,
                onTapAsync: () async =>
                    controller.onStartKycTap(isContinue: true),
                radius: Sizes.RADIUS_12,
                constraints: const BoxConstraints(minHeight: 55),
              ),
          ],
        ),
      ),
    );
  }
}
