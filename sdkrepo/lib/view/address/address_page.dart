import 'package:example/view_model/controllers/address_controller/address_controller.dart';

import '../../resources/exports/index.dart';
import '../../view_model/controllers/merchant_login_controller/merchant_login_controller.dart';

class AddressPage extends GetView<AddressController> {
  AddressPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    var args = Get.arguments;
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: const CustomAppBar(title: "Full address", backallow: false),
      body: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 24),
        child: GetBuilder<AddressController>(
          id: 'page',
          builder: (_) {
            if (controller.isLoading.value) {
              return Center(
                child: CircularProgressIndicator(),
              );
            } else {
              return SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SpaceH20(),
                    const SpaceH20(),
                    CustomTextFormField(
                      controller: controller.addressLine1Ctl,
                      isRequired: true,
                      height: Sizes.HEIGHT_20,
                      labelText: "Address line 1",
                      labelColor: AppColors.black,
                      prefixIconColor: AppColors.black,
                      textColor: AppColors.black,
                      cursorColor: AppColors.black,
                      errorColor: AppColors.black,
                      enableBorderColor: AppColors.black,
                      focusBorderColor: AppColors.primary,
                      textInputAction: TextInputAction.next,
                      keyboardType: TextInputType.text,
                    ),
                    const SpaceH20(),
                    CustomTextFormField(
                      controller: controller.addressLine2Ctl,
                      isRequired: true,
                      height: Sizes.HEIGHT_20,
                      labelText: "Address line 2",
                      labelColor: AppColors.black,
                      prefixIconColor: AppColors.black,
                      textColor: AppColors.black,
                      cursorColor: AppColors.black,
                      errorColor: AppColors.black,
                      enableBorderColor: AppColors.black,
                      focusBorderColor: AppColors.primary,
                      textInputAction: TextInputAction.next,
                      keyboardType: TextInputType.text,
                    ),
                    const SpaceH20(),
                    CustomTextFormField(
                      controller: controller.countryCtl,
                      isRequired: true,
                      height: Sizes.HEIGHT_20,
                      labelText: "Country",
                      labelColor: AppColors.black,
                      prefixIconColor: AppColors.black,
                      textColor: AppColors.black,
                      cursorColor: AppColors.black,
                      errorColor: AppColors.black,
                      enableBorderColor: AppColors.black,
                      focusBorderColor: AppColors.primary,
                      textInputAction: TextInputAction.next,
                      keyboardType: TextInputType.text,
                    ),
                    const SpaceH20(),
                    CustomTextFormField(
                      controller: controller.cityCtl,
                      isRequired: true,
                      height: Sizes.HEIGHT_20,
                      labelText: "City",
                      labelColor: AppColors.black,
                      prefixIconColor: AppColors.black,
                      textColor: AppColors.black,
                      cursorColor: AppColors.black,
                      errorColor: AppColors.black,
                      enableBorderColor: AppColors.black,
                      focusBorderColor: AppColors.primary,
                      textInputAction: TextInputAction.next,
                      keyboardType: TextInputType.text,
                    ),
                    const SpaceH20(),
                    CustomTextFormField(
                      controller: controller.postalCodeCtl,
                      isRequired: true,
                      height: Sizes.HEIGHT_20,
                      labelText: "Postal code",
                      labelColor: AppColors.black,
                      prefixIconColor: AppColors.black,
                      textColor: AppColors.black,
                      cursorColor: AppColors.black,
                      errorColor: AppColors.black,
                      enableBorderColor: AppColors.black,
                      focusBorderColor: AppColors.primary,
                      textInputAction: TextInputAction.next,
                      keyboardType: TextInputType.text,
                    ),
                    const SpaceH20(),
                    GetBuilder<AddressController>(
                      id: 'button',
                      builder: (_) {
                        if (controller.isLoading.value ?? false) {
                          return CircularProgressIndicator();
                        } else {
                          return CustomButton.solid(
                            backgroundColor: AppColors.primary,
                            textColor: AppColors.white,
                            text: "Submit",
                            onTapAsync: () async {

                              controller.submitAddress(
                                  args['address'], args['lat'], args['lng']);
                            },
                            radius: Sizes.RADIUS_12,
                            constraints: const BoxConstraints(minHeight: 55),
                          );
                        }
                      },
                    ),
                  ],
                ),
              );
            }
          },
        ),
      ),
    );
  }
}
