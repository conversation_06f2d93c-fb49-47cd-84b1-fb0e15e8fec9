import 'package:digital_onboarding/digital_onboarding.dart';
import 'package:example/view_model/extra_documents/extra_documents_controller.dart';

import '../../../resources/exports/index.dart';

class DocumentTypeDropDown extends GetView<ExtraDocumentsController> {
  DocumentTypeDropDown({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (controller.isLoading.value ?? false) {
      return Center(
        child: CircularProgressIndicator(),
      );
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Column(
        children: [
          Text(
            "Please choose document type",
            style: TextStyle(fontSize: 20),
          ),
          const SpaceH30(),
          GetBuilder<ExtraDocumentsController>(
            id: 'multi_choice_dropdown',
            builder: (_) {
              return DropdownButtonHideUnderline(
                child: DropdownButton2<DigitalOnBoardingDocumentTypeModel>(
                  isExpanded: true,
                  items: controller?.documentsTypes
                          ?.map(
                            (DigitalOnBoardingDocumentTypeModel? item) =>
                                DropdownMenuItem<
                                    DigitalOnBoardingDocumentTypeModel>(
                              value: item,
                              child: Text(
                                item?.documentType ?? "",
                                style: context.titleMedium,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          )
                          .toList() ??
                      [],
                  value: controller.selectedItem,
                  onChanged: (v) {
                    controller.toggleMultiChoiceAnswer(v);
                  },
                  buttonStyleData: ButtonStyleData(
                    height: 50,
                    width: double.maxFinite,
                    padding: const EdgeInsets.only(left: 14, right: 14),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(14),
                      border: Border.all(
                        color: Colors.black26,
                      ),
                      color: AppColors.white,
                    ),
                    elevation: 2,
                  ),
                  iconStyleData: const IconStyleData(
                    icon: Icon(Icons.arrow_forward_ios_outlined),
                    iconSize: 14,
                    iconEnabledColor: AppColors.black,
                    iconDisabledColor: Colors.grey,
                  ),
                  dropdownStyleData: DropdownStyleData(
                    maxHeight: 200,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(14),
                      color: AppColors.white,
                    ),
                    offset: const Offset(0, 0),
                    scrollbarTheme: ScrollbarThemeData(
                      radius: const Radius.circular(40),
                      thickness: MaterialStateProperty.all<double>(6),
                      thumbVisibility: MaterialStateProperty.all<bool>(true),
                    ),
                  ),
                  menuItemStyleData: const MenuItemStyleData(
                    height: 40,
                    padding: EdgeInsets.symmetric(horizontal: 14),
                  ),
                ),
              );
            },
          ),
          const SpaceH60(),
          SizedBox(
            height: 60,
            child: CustomButton.solid(
              backgroundColor: AppColors.primary,
              textColor: AppColors.white,
              text: "Next",
              onTap: () {
                controller.pickImage();
              },
              radius: Sizes.RADIUS_12,
              constraints: const BoxConstraints(minHeight: 55),
            ),
          ),
        ],
      ),
    );
  }
}
