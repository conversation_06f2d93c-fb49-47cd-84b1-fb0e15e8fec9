import '../../resources/exports/index.dart';

class QAScreen extends GetView<QAController> {
  const QAScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: const CustomAppBar(title: Strings.QUESTIONS, backallow: true),
      body: Obx(() {
        if (controller.isLoading.value) {
          return Center(
            child: CircularProgressIndicator(color: AppColors.primary),
          );
        }
        // Show the actual content when loading is complete
        return Column(
          children: [
            GetBuilder<QAController>(
              id: 'current_question_no',
              builder: (_) {
                return Center(
                  child: Text(
                    '${controller.index} / ${controller.numberOfQuestions}',
                    style: context.headlineSmall.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                );
              },
            ),
            PageView(
              physics: const NeverScrollableScrollPhysics(),
              controller: controller.pageCtrl,
              children: controller.questionsList?.map((e) {
                    if (e?.questionType == "fill-in-the-blank") {
                      return FillTheBlankQuestion(questionModel: e);
                    }
                    return MultieChoiceQuestion(
                      questionModel: e,
                    );
                  }).toList() ??
                  [],
            ).paddingSymmetric(horizontal: 16.0).constrainedBox(maxHeight: 350),
            const SpaceH20(),
            Row(
              children: [
                CustomButton.solid(
                  backgroundColor: AppColors.primary,
                  textColor: AppColors.white,
                  text: Strings.PREVIOUS,
                  onTap: controller.onPreviousTap,
                  radius: Sizes.RADIUS_12,
                  constraints: const BoxConstraints(minHeight: 55),
                ).expanded(),
                const SpaceW16(),
                GetBuilder<QAController>(
                  id: 'qa_btn',
                  builder: (_) {
                    return CustomButton.solid(
                      backgroundColor: AppColors.primary,
                      textColor: AppColors.white,
                      text: controller.index == 5 ? Strings.DONE : Strings.NEXT,
                      onTap: controller.onNextTap,
                      radius: Sizes.RADIUS_12,
                      constraints: const BoxConstraints(minHeight: 55),
                    ).expanded();
                  },
                ),
              ],
            ).paddingSymmetric(horizontal: 16.0),
            const SpaceH20(),
          ],
        );
      }),
    );
  }
}
