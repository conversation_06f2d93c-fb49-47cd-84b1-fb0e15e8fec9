import 'package:digital_onboarding/digital_onboarding.dart';

import '../../../resources/exports/index.dart';

class MultieChoiceQuestion extends GetView<QAController> {
  DigitalOnBoardingQuestionModel? questionModel;

  MultieChoiceQuestion({
    super.key,
    required this.questionModel,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SpaceH30(),
        Text("Q${controller.index} : ${questionModel?.question ?? ''}",
            style: context.titleLarge),
        Sized<PERSON>ox(
          height: 50,
        ),
        GetBuilder<QAController>(
          id: 'multi_choice_dropdown',
          builder: (_) {
            return DropdownButtonHideUnderline(
              child: DropdownButton2<DigitalOnBoardingAnswerOption>(
                isExpanded: true,
                items: questionModel?.answers
                        ?.map(
                          (DigitalOnBoardingAnswerOption item) =>
                              DropdownMenuItem<DigitalOnBoardingAnswerOption>(
                            value: item,
                            child: Text(
                              item.answer ?? "",
                              style: context.titleMedium,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        )
                        .toList() ??
                    [],
                value: controller.selectedItems.isNotEmpty
                    ? controller.selectedItems[controller.index]
                    : null,
                onChanged: (v) {
                  controller.toggleMultiChoiceAnswer(v, controller.index);
                },
                buttonStyleData: ButtonStyleData(
                  height: 50,
                  width: double.maxFinite,
                  padding: const EdgeInsets.only(left: 14, right: 14),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(14),
                    border: Border.all(
                      color: Colors.black26,
                    ),
                    color: AppColors.white,
                  ),
                  elevation: 2,
                ),
                iconStyleData: const IconStyleData(
                  icon: Icon(Icons.arrow_forward_ios_outlined),
                  iconSize: 14,
                  iconEnabledColor: AppColors.black,
                  iconDisabledColor: Colors.grey,
                ),
                dropdownStyleData: DropdownStyleData(
                  maxHeight: 200,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(14),
                    color: AppColors.white,
                  ),
                  offset: const Offset(0, 0),
                  scrollbarTheme: ScrollbarThemeData(
                    radius: const Radius.circular(40),
                    thickness: MaterialStateProperty.all<double>(6),
                    thumbVisibility: MaterialStateProperty.all<bool>(true),
                  ),
                ),
                menuItemStyleData: const MenuItemStyleData(
                  height: 40,
                  padding: EdgeInsets.symmetric(horizontal: 14),
                ),
              ),
            );
          },
        ),
        const SpaceH16(),
        GetBuilder<QAController>(
          id: 'purpose_dropdown',
          builder: (_) {
            return Visibility(
              visible: controller.selectedItems.isNotEmpty &&
                  controller.selectedItems[controller.index] == Strings.OTHER,
              child: CustomTextFormField(
                controller: controller.purposeCtrl,
                isRequired: true,
                maxLines: 5,
                height: Sizes.HEIGHT_20,
                labelText: Strings.ANSWER,
                labelColor: AppColors.black,
                textColor: AppColors.black,
                cursorColor: AppColors.black,
                errorColor: AppColors.black,
                enableBorderColor: AppColors.black,
                focusBorderColor: AppColors.primary,
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.name,
              ),
            );
          },
        ),
      ],
    );
  }
}
