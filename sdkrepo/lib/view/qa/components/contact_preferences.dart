import '../../../resources/exports/index.dart';

class ContactPreferences extends GetView<QAController> {
  const ContactPreferences({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SpaceH30(),
        Text("Q6 : ${Strings.CONTACT_PREFERENCES}", style: context.titleLarge),
        const SpaceH30(),
        CustomTextFormField(
          controller: controller.prefEmailCtrl,
          height: Sizes.HEIGHT_20,
          labelText: Strings.EMAIL,
          labelColor: AppColors.black,
          textColor: AppColors.black,
          cursorColor: AppColors.black,
          errorColor: AppColors.black,
         
          enableBorderColor: AppColors.black,
          focusBorderColor: AppColors.primary,
          textInputAction: TextInputAction.next,
          keyboardType: TextInputType.name,
          validator: Validators.emailValidation,
        ),
        const SpaceH12(),
        CustomTextFormField(
          controller: controller.addressCtrl,
          height: Sizes.HEIGHT_20,
          labelText: Strings.ADDRESS,
          maxLines: 5,
          labelColor: AppColors.black,
          textColor: AppColors.black,
          cursorColor: AppColors.black,
          errorColor: AppColors.black,
          enableBorderColor: AppColors.black,
          focusBorderColor: AppColors.primary,
          textInputAction: TextInputAction.next,
          keyboardType: TextInputType.name,
        ),
      ],
    );
  }
}
