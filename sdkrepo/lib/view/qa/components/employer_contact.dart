import '../../../resources/exports/index.dart';

class EmployerContact extends GetView<QAController> {
  const EmployerContact({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SpaceH30(),
        Text("Q4 : ${Strings.EMPLOYER_CONTACT}", style: context.titleLarge),
        const SpaceH60(),
        CustomTextFormField(
          controller: controller.empContactCtrl,
          height: Sizes.HEIGHT_20,
          labelText: Strings.EMP_CONTACT,
          maxLines: 5,
          labelColor: AppColors.black,
          textColor: AppColors.black,
          cursorColor: AppColors.black,
          errorColor: AppColors.black,
          enableBorderColor: AppColors.black,
          focusBorderColor: AppColors.primary,
          textInputAction: TextInputAction.next,
          keyboardType: TextInputType.name,
          validator: Validators.emailValidation,
        ),
      ],
    );
  }
}
