import 'package:digital_onboarding/digital_onboarding.dart';

import '../../../resources/exports/index.dart';

class FillTheBlankQuestion extends GetView<QAController> {
  DigitalOnBoardingQuestionModel? questionModel;

  FillTheBlankQuestion({
    super.key,
    required this.questionModel,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SpaceH30(),
        Text("Q${controller.index} : ${questionModel?.question ?? ""}",
            style: context.titleLarge),
        const SpaceH60(),
        CustomTextFormField(
          key: Key("${questionModel?.id}"),
          onChanged: (value) {
            controller.toggleMultiChoiceAnswer(
                DigitalOnBoardingAnswerOption(
                  questionId: questionModel?.id ?? 0,
                  answer: value,
                ),
                controller.index);
          },
          controller: controller.textEditingControllers[controller.index - 1],
          isRequired: true,
          maxLines: 5,
          height: Sizes.HEIGHT_20,
          labelText: Strings.ANSWER,
          labelColor: AppColors.black,
          textColor: AppColors.black,
          cursorColor: AppColors.black,
          errorColor: AppColors.black,
          enableBorderColor: AppColors.black,
          focusBorderColor: AppColors.primary,
          textInputAction: TextInputAction.next,
          keyboardType: TextInputType.name,
        ),
      ],
    );
  }
}
