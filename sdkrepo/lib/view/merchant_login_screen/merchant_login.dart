import '../../resources/exports/index.dart';
import '../../view_model/controllers/merchant_login_controller/merchant_login_controller.dart';

class MerchantLogin extends GetView<MerchantLoginController> {
  const MerchantLogin({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: const CustomAppBar(title: Strings.LOGIN, backallow: false),
      body: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 24),
        child: GetBuilder<MerchantLoginController>(
          id: 'page',
          builder: (_) {
            if (controller.isLoading.value) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            } else {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SpaceH20(),
                  Text(Strings.ENTER_YOUR_USER_NAME, style: context.headlineSmall),
                  const SpaceH20(),
                  CustomTextFormField(
                    controller: controller.userNameCtl,
                    isRequired: true,
                    height: Sizes.HEIGHT_20,
                    labelText: Strings.USER_NAME,
                    labelColor: AppColors.black,
                    prefixIcon: Icons.person,
                    prefixIconColor: AppColors.black,
                    textColor: AppColors.black,
                    cursorColor: AppColors.black,
                    errorColor: AppColors.black,
                    enableBorderColor: AppColors.black,
                    focusBorderColor: AppColors.primary,
                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.text,
                  ),
                  const SpaceH20(),
                  CustomTextFormField(
                    controller: controller.passwordCtl,
                    isRequired: true,
                    height: Sizes.HEIGHT_20,
                    labelText: Strings.password,
                    labelColor: AppColors.black,
                    prefixIcon: Icons.password,
                    prefixIconColor: AppColors.black,
                    textColor: AppColors.black,
                    cursorColor: AppColors.black,
                    errorColor: AppColors.black,
                    enableBorderColor: AppColors.black,
                    focusBorderColor: AppColors.primary,
                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.text,
                  ),
                  const SpaceH20(),
                  CustomButton.solid(
                    backgroundColor: AppColors.primary,
                    textColor: AppColors.white,
                    text: Strings.LOGIN,
                    onTapAsync: () async => controller.merchantLogin(true),
                    radius: Sizes.RADIUS_12,
                    constraints: const BoxConstraints(minHeight: 55),
                  ),
                ],
              );
            }
          },
        ),
      ),
    );
  }
}
