import '../../resources/exports/index.dart';
import 'package:signature/signature.dart';

class SignatureScreen extends GetView<SignController> {
  const SignatureScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: const CustomAppBar(title: Strings.SIGNATURE,backallow:true),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          GetBuilder<SignController>(
            id: 'signature',
            builder: (_) => Signature(
              key: const Key('signature'),
              controller: controller.signatureCtrl,
              height: 300,
              backgroundColor: Colors.grey[300]!,
            ),
          ),
          const SpaceH20(),
          Row(
            children: [
              CustomButton.solid(
                backgroundColor: AppColors.primary,
                textColor: AppColors.white,
                text: Strings.CLEAR,
                onTap: controller.clearSignature,
                radius: Sizes.RADIUS_12,
                constraints: const BoxConstraints(minHeight: 55),
              ).expanded(),
              const SpaceW16(),
              CustomButton.solid(
                backgroundColor: AppColors.primary,
                textColor: AppColors.white,
                text: Strings.DONE,
                onTap: controller.exportImage,
                radius: Sizes.RADIUS_12,
                constraints: const BoxConstraints(minHeight: 55),
              ).expanded(),
            ],
          ).paddingSymmetric(horizontal: 16.0),
        ],
      ),
    );
  }
}
