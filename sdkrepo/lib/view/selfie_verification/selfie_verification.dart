import 'package:face_camera/face_camera.dart';
import '../../resources/exports/index.dart';

class SelfieVerification extends GetView<SelfieVerificationController> {
  const SelfieVerification({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: Strings.SELFIE_VERIFICTION,backallow:true),
      backgroundColor: AppColors.white,
      body: Builder(
        builder: (context) {
          if (controller.image != null) {
            return Center(
              child: Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  Image.file(
                    File(controller.image!.path),
                    width: double.maxFinite,
                    fit: BoxFit.fitWidth,
                  ),
                  ElevatedButton(
                    onPressed: () {
                      controller.image = null;
                    },
                    child: const Text(
                      'Capture',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }
          return SmartFaceCamera(
            defaultCameraLens: CameraLens.front,
            onCapture: (File? image) async {
              if (image != null) {
                controller.image = XFile(image.path);
                await controller.onVerify();
              }
            },
          );
        },
      ),
    );
  }
}
