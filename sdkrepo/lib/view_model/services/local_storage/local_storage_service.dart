import '../../../resources/exports/index.dart';

enum CacheManagerKeys {
  token,
  sessionData,
  appLanguage,
  deviceToken,
  sessionToken,
}

class LocalStorageService extends GetxService {
//**************************** Start Session Token For IQID KYC ************************** */

  Future<bool> saveSessionToken(String? token) async {
    final box = GetStorage(Strings.CACHE_BOX_KEY);
    await box.write(CacheManagerKeys.sessionToken.toString(), token ?? "");
    return true;
  }

  String? getSessionToken() {
    final box = GetStorage(Strings.CACHE_BOX_KEY);
    return box.read(CacheManagerKeys.sessionToken.toString());
  }

//**************************** End Session Token For IQID KYC ************************** */

  Future<bool> saveDeviceToken(String? token) async {
    final box = GetStorage(Strings.CACHE_BOX_KEY);
    await box.write(CacheManagerKeys.deviceToken.toString(), token ?? "");
    return true;
  }

  String? getDeviceToken() {
    final box = GetStorage(Strings.CACHE_BOX_KEY);
    return box.read(CacheManagerKeys.deviceToken.toString());
  }

  Future<bool> saveDefaultLanguage(String? language) async {
    final box = GetStorage(Strings.CACHE_BOX_KEY);
    await box.write(CacheManagerKeys.appLanguage.toString(), language ?? "en");
    return true;
  }

  String? getDefaultLanguage() {
    final box = GetStorage(Strings.CACHE_BOX_KEY);
    return box.read(CacheManagerKeys.appLanguage.toString());
  }

  Future<bool> saveToken(String? token) async {
    final box = GetStorage(Strings.CACHE_BOX_KEY);
    await box.write(CacheManagerKeys.token.toString(), token);
    return true;
  }

  Future<bool> saveSession(Session? session) async {
    final box = GetStorage(Strings.CACHE_BOX_KEY);
    await box.write(CacheManagerKeys.sessionData.toString(), session!.toJson());
    log.e("SAVING SESSION : ${session.toJson()}");
    return true;
  }

  String? getToken() {
    final box = GetStorage(Strings.CACHE_BOX_KEY);
    return box.read(CacheManagerKeys.token.toString());
  }

  Session? getSessionData() {
    final box = GetStorage(Strings.CACHE_BOX_KEY);
    return Session.fromJson(box.read(CacheManagerKeys.sessionData.toString()));
  }

  Future<void> removeToken() async {
    final box = GetStorage(Strings.CACHE_BOX_KEY);
    await box.remove(CacheManagerKeys.token.toString());
    await box.remove(CacheManagerKeys.sessionData.toString());
    await box.remove(CacheManagerKeys.sessionToken.toString());
  }
}
