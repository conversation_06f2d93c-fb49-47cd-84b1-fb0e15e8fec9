import 'package:digital_onboarding/digital_onboarding.dart';

import '../../../resources/exports/index.dart';
import 'package:signature/signature.dart';

class ExtraDocumentsController extends GetxController {
  List<DigitalOnBoardingDocumentTypeModel?>? documentsTypes;
  var isLoading = true.obs;
  DigitalOnBoardingDocumentTypeModel? selectedItem;

  toggleMultiChoiceAnswer(value) {
    selectedItem = value;

    update(['multi_choice_dropdown']);
  }

  Future<void> onVerify(XFile file) async {
    GlobalHelper.showPopupLoader();

    try {
      await DigitalOnboardingServices.submitDocument(file,
          step: StepEnum.signature);
      Get.back();
      Get.offAllNamed(Routes.DASHBOARD);
    } on DigitalOnboardingException catch (e) {}
  }

  @override
  void onInit() {
    getDocsTypes();
    super.onInit();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> getDocsTypes() async {
    try {
      documentsTypes = await DigitalOnboardingServices.getDocumentTypes();
      isLoading.value = false;
      update(['page_update']);
    } on DigitalOnboardingException catch (e) {
      isLoading.value = false;
      update(['page_update']);
      return CustomSnackBar.errorSnackBar(
        message: Strings.SOMETHING_WENT_WRONG,
      );
    } catch (e) {
      isLoading.value = false;
      update(['page_update']);
    }
  }

  void pickImage() async {
    XFile? image =
        await ImagePickerService.pickImage(imageSource: ImageSource.camera);
    if (image != null) {
      CustomDialog.showDialog(
        onSave: () => updateDocs(image),
        title: Strings.Extra_DOCS,
        content: Container(
            color: AppColors.white,
            child: Center(
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                  ImageService.image(
                    image.path,
                    borderRadius: 12.0,
                    imageHeight: 200,
                    imageWidth: 200,
                  )
                ]))),
      );
    }
  }

  Future<void> updateDocs(XFile image) async {
    Get.back();

    GlobalHelper.showPopupLoader();

    try {
      await DigitalOnboardingServices.submitDocument(image,
          step: StepEnum.extraDoc, extraData: selectedItem?.identifier);

      Get.back();
      Get.offAllNamed(Routes.DASHBOARD);
    } on DigitalOnboardingException catch (e) {
      Get.back();
      CustomSnackBar.errorSnackBar(
          message: e.message ?? Strings.SOMETHING_WENT_WRONG);
    } catch (e) {
      Get.back();
      CustomSnackBar.errorSnackBar(message: Strings.SOMETHING_WENT_WRONG);
    }
  }
}
