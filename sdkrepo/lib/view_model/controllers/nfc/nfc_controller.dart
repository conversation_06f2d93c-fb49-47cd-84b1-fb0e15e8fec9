import 'package:digital_onboarding/digital_onboarding.dart';

import '../../../resources/exports/index.dart';

class NFCController extends GetxController {
  String? base64Image;

  Future<void> onVerify(
      Map<String, dynamic>? mrzData, Uint8List? jpegImage) async {
    GlobalHelper.showPopupLoader();
    if (jpegImage != null) {
      base64Image = base64Encode(jpegImage);

      XFile file = await GlobalHelper().getFileFromUint8List(jpegImage, "nfc");

      try {
        await DigitalOnboardingServices.submitDocument(file,
            step: StepEnum.nfc, extraData: json.encode(mrzData));
        Get.back();
        Get.offAllNamed(Routes.DASHBOARD);
      } on DigitalOnboardingException catch (e) {

      }
    }

  }
}
