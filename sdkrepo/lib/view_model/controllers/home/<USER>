import 'package:digital_onboarding/digital_onboarding.dart';
import '../../../resources/exports/index.dart';

class HomeController extends GetxController {
  bool isexist = AuthManager.instance.sessionToken.isNotEmpty;

  Future<void> onStartKycTap({bool? isContinue}) async {
    AuthManager.instance.logout(shouldNavigate: false);
    try {

    } on DigitalOnboardingException catch (e) {
      CustomSnackBar.errorSnackBar(
          message: e.message ?? Strings.SOMETHING_WENT_WRONG);
    } catch (e) {}
  }
}
