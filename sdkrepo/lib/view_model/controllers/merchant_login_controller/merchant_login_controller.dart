import 'package:digital_onboarding/digital_onboarding.dart';
import '../../../resources/exports/index.dart';

class MerchantLoginController extends GetxController with GetSingleTickerProviderStateMixin {
  late TextEditingController userNameCtl;
  late TextEditingController passwordCtl;

  RxBool isLoading = true.obs;

  Future<void> merchantLogin(bool isContinue) async {
    try {
      await DigitalOnboardingServices.configureDigitalOnboardingService(
        shouldCreateNewSession: isContinue,
        username: userNameCtl.text,
        password: passwordCtl.text,
        key: FlutterConfig.get("appId"),
        encryptionKey: publicPem,
      );

      Get.offAllNamed(Routes.DASHBOARD);
    } on DigitalOnboardingException catch (e) {
      CustomSnackBar.errorSnackBar(message: e.message ?? Strings.SOMETHING_WENT_WRONG);
    } catch (e) {
      CustomSnackBar.errorSnackBar(message: Strings.SOMETHING_WENT_WRONG);
    }
  }

  @override
  void onInit() async {
    userNameCtl = TextEditingController(text: "demo2");
    passwordCtl = TextEditingController(text: "password");

    isLoading.value = false;
    update(["page"]);
    super.onInit();
  }
}
