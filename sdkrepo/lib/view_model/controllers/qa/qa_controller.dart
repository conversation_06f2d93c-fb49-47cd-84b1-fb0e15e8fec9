import 'package:digital_onboarding/digital_onboarding.dart';

import '../../../resources/exports/index.dart';

class Q<PERSON>ontroller extends GetxController {
  List<DigitalOnBoardingQuestionModel?>? questionsList;
  int numberOfQuestions = 0;
  var isLoading = true.obs;

  late PageController pageCtrl;
  late int index;

  QAModel qaModel = QAModel();

  // Job Title
  late TextEditingController jobTitleCtrl;
  List<TextEditingController> textEditingControllers = [];

  // Employment Type
  String selectedEmpTypeItem = Strings.FULLTIME;

  List<String> empTypeitems = [
    Strings.FULLTIME,
    Strings.CONTRACT,
    Strings.PART_TIME,
    Strings.FREELANCE,
    Strings.SELF_EMPLOYED,
    Strings.INTERN,
    Strings.VOLUNTEER,
    Strings.STUDENT,
    Strings.UNEMPLOYED,
  ];

  void toggleMultiChoiceAnswer(
      DigitalOnBoardingAnswerOption? value, int index) {
    selectedItems[index] = value;

    update(['multi_choice_dropdown']);
  }

  void toggleEmpType(String? value) {
    if (value == null) {
      CustomSnackBar.errorSnackBar(message: Strings.SOMETHING_WENT_WRONG);
      return;
    }
    selectedEmpTypeItem = value;
    update(['emp_type_dropdown']);
  }

  // Employer Contact
  late TextEditingController empContactCtrl;

  // Salary Range
  String salaryRangeItem = Strings.BELOW_600K;

  List<String> salaryRangeItems = [
    Strings.BELOW_600K,
    Strings.SIX_NINE,
    Strings.MIL_ONE_HALF_MIL,
    Strings.ONE_HALF_MIL_TWO_MIL,
    Strings.MIL_TWO_MIL_TOW_HAL_MIL,
    Strings.TOW_HALF_MIL_THREE_MIL,
    Strings.MIL_THREE_MIL_THREE_EIGHT_MIL,
    Strings.FOUR_MIL_SIX_MIL,
    Strings.ABOVE_SIX_MIL,
  ];

  void toggleSalaryRange(String? value) {
    if (value == null) {
      CustomSnackBar.errorSnackBar(message: Strings.SOMETHING_WENT_WRONG);
      return;
    }
    salaryRangeItem = value;
    update(['salary_range_dropdown']);
  }

  // Contact Preferences
  late TextEditingController prefEmailCtrl;
  late TextEditingController addressCtrl;

  // Purpose of requesting
  late TextEditingController purposeCtrl;
  Map<int, DigitalOnBoardingAnswerOption?> selectedItems = {};

  List<String> items = [
    Strings.BUILDING_CREDIT,
    Strings.EVERYDAY_PURCHASES,
    Strings.TRAVEL,
    Strings.ONLINE_SHOPPING,
    Strings.LARGE_PURCHASES,
    Strings.BUSINESS_EXPENSES,
    Strings.OTHER,
  ];

  void togglePurpose(DigitalOnBoardingAnswerOption? value) {
    if (value == null) {
      CustomSnackBar.errorSnackBar(message: Strings.SOMETHING_WENT_WRONG);
      return;
    }
    selectedItems[0] = value;
    update(['purpose_dropdown']);
  }

  List<QAModel> questions = [];

  void onNextTap() {


    if (index == numberOfQuestions) onVerify(qaModel.toJson());

    if (pageCtrl.page == 5.0) return;

    pageCtrl.nextPage(
      duration: Durations.medium1,
      curve: Curves.linear,
    );
    index++;
    update(['current_question_no', 'qa_btn']);
  }

  Future<void> onVerify(Map<String, dynamic> qaData) async {
    try {
      GlobalHelper.showPopupLoader();


      List<DigitalOnBoardingUserAnswerModel> userAnswers = [];

      selectedItems.forEach((key, value) {
        if (value != null) {
          userAnswers.add(
            DigitalOnBoardingUserAnswerModel(
              answerId: value.id,
              questionId: value.questionId,
              answerText: value.answer,
            ),
          );
        }
      });

      await DigitalOnboardingServices.submitUserAnswers(
          userAnswers: userAnswers);

      Get.back();

      Get.offAllNamed(Routes.DASHBOARD);
    } on DigitalOnboardingException catch (e) {
      Get.back();
      CustomSnackBar.errorSnackBar(
          message: e.message ?? Strings.SOMETHING_WENT_WRONG);
    } catch (e) {
      Get.back();
      CustomSnackBar.errorSnackBar(message: Strings.SOMETHING_WENT_WRONG);
    }
  }

  void onPreviousTap() {
    if (pageCtrl.page == 0.0) return;
    pageCtrl.previousPage(
      duration: Durations.medium1,
      curve: Curves.linear,
    );
    index--;
    update(['current_question_no', 'qa_btn']);
  }

  @override
  void onInit() async {
    pageCtrl = PageController(initialPage: 0);
    jobTitleCtrl = TextEditingController();
    empContactCtrl = TextEditingController();
    prefEmailCtrl = TextEditingController();
    addressCtrl = TextEditingController();
    purposeCtrl = TextEditingController();
    index = 1;

    getQuestion();

    super.onInit();
  }

  @override
  void dispose() {
    index = 0;
    jobTitleCtrl.dispose();
    empContactCtrl.dispose();
    prefEmailCtrl.dispose();
    addressCtrl.dispose();
    purposeCtrl.dispose();
    super.dispose();
  }

  Future<void> getQuestion() async {
    try {
      questionsList = await DigitalOnboardingServices.getQuestions();
      numberOfQuestions = questionsList?.length ?? 0;
      textEditingControllers = List.generate(
          questionsList?.length ?? 0, (index) => TextEditingController());

      isLoading.value = false;
      update(['page_update']);
    } on DigitalOnboardingException catch (e) {
      isLoading.value = false;
      return CustomSnackBar.errorSnackBar(
        message: Strings.SOMETHING_WENT_WRONG,
      );
    } catch (e) {
      isLoading.value = false;
    }
  }
}
