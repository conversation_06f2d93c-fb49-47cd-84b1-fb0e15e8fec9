import 'package:digital_onboarding/digital_onboarding.dart';

import '../../../resources/exports/index.dart';
import 'package:signature/signature.dart';


class SignController extends GetxController {
  late SignatureController signatureCtrl;

  void clearSignature() {
    signatureCtrl.clear();
    update(['signature']);
  }

  Future<void> exportImage() async {
    if (signatureCtrl.isEmpty) {
      CustomSnackBar.errorSnackBar(message: Strings.PLEASE_DRAW);
      return;
    }
    final Uint8List? data = await signatureCtrl.toPngBytes(
      height: 600,
      width: 600,
    );

    if (data == null) {
      CustomSnackBar.errorSnackBar(message: Strings.IMAGE_FIRST);
      return;
    }

    XFile file = await GlobalHelper().getFileFromUint8List(data, "signature");

    CustomDialog.showDialog(
      onSave: () => onVerify(file),
      title: Strings.SIGNATURE,
      content: Container(
        color: AppColors.white,
        child: Image.memory(data),
      ),
    );
  }

  Future<void> onVerify(XFile file) async {
    GlobalHelper.showPopupLoader();

    try {
      await DigitalOnboardingServices.submitDocument(file,
          step: StepEnum.signature);
      Get.back();
      Get.offAllNamed(Routes.DASHBOARD);
    } on DigitalOnboardingException catch (e) {
      CustomSnackBar.errorSnackBar(
          message: e.message ?? Strings.SOMETHING_WENT_WRONG);
    }
  }

  @override
  void onInit() {
    signatureCtrl = SignatureController(
      penStrokeWidth: 2,
      penColor: AppColors.primary,
      exportBackgroundColor: Colors.transparent,
      exportPenColor: Colors.black,
      onDrawStart: () {},
      onDrawEnd: () {},
    );
    super.onInit();
  }

  @override
  void dispose() {
    signatureCtrl.dispose();
    super.dispose();
  }

}
