import 'package:digital_onboarding/digital_onboarding.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../resources/exports/index.dart';

class SelfieVerificationController extends GetxController {
  XFile? image;

  Future<void> updateImage() async {
    update(['doc_picture']);
  }

  Future<void> onVerify() async {
    if (image == null) {
      CustomSnackBar.errorSnackBar(message: Strings.IMAGE_FIRST);
      return;
    }
    GlobalHelper.showPopupLoader();

    try {
      await DigitalOnboardingServices.submitDocument(image!,
          step: StepEnum.selfie);

      Get.back();
      Get.offAllNamed(Routes.DASHBOARD);
    } on DigitalOnboardingException catch (e) {
      CustomSnackBar.errorSnackBar(message: Strings.SOMETHING_WENT_WRONG);
    }
  }

  @override
  Future<void> onReady() async {
    await Permission.camera.request();
    super.onInit();
  }
}
