import 'package:digital_onboarding/digital_onboarding.dart';
import 'package:intl_phone_field/countries.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../data/network/base_api_services.dart';
import '../../../data/network/network_api_services.dart';
import '../../../resources/exports/index.dart';

class AddressController extends GetxController
    with GetSingleTickerProviderStateMixin {
  late TextEditingController addressLine1Ctl;
  late TextEditingController addressLine2Ctl;
  late TextEditingController cityCtl;
  late TextEditingController countryCtl;
  late TextEditingController postalCodeCtl;

  RxBool isLoading = false.obs;

  Future<void> submitAddress(String address, double? lat, double? lang) async {
    try {
      isLoading.value = true;
      update(['button']);
      final result = await DigitalOnboardingServices.uploadLocationData(
        address: address,
        lat: lat,
        lng: lang,
        streetAddress1: addressLine1Ctl.text,
        streetAddress2: addressLine2Ctl.text,
        city: cityCtl.text,
        country: countryCtl.text,
        postalCode: postalCodeCtl.text,
      );




      Get.offAllNamed(Routes.DASHBOARD);
      isLoading.value = false;
      update(['button']);
    } on DigitalOnboardingException catch (e) {
      CustomSnackBar.errorSnackBar(
          message: e.message ?? Strings.SOMETHING_WENT_WRONG);

      isLoading.value = false;
      update(['button']);
    } catch (e) {
      CustomSnackBar.errorSnackBar(message: Strings.SOMETHING_WENT_WRONG);
      isLoading.value = false;
      update(['button']);
    }
  }

  @override
  void onInit() async {
    addressLine1Ctl = TextEditingController();
    addressLine2Ctl = TextEditingController();
    cityCtl = TextEditingController();
    countryCtl = TextEditingController();
    postalCodeCtl = TextEditingController();

    update(["page"]);
    super.onInit();
  }
}
