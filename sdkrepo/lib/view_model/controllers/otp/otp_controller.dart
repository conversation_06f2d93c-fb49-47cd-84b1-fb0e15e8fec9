import 'package:digital_onboarding/digital_onboarding.dart';

import '../../../resources/exports/index.dart';

class OtpController extends GetxController {
  Timer? otpTimer;
  Duration myDuration = const Duration(minutes: 5);
  bool isLoading = false;

  late SessionOtpModel otpModel;
  late String type;

  late TextEditingController pinCtrl;

  Future<void> updateSession() async {
    try {
      final bool isVerified = await DigitalOnboardingServices.verifyOtp(
          loginType: otpModel.email == null
              ? DigitalOnBoardingLoginType.phone
              : DigitalOnBoardingLoginType.email,
          value: otpModel.email ?? otpModel.phone ?? "",
          otp: pinCtrl.text);

      if (!isVerified) {
        return CustomSnackBar.errorSnackBar(
          message: Strings.SOMETHING_WENT_WRONG,
        );
      }

      Get.offAllNamed(Routes.DASHBOARD);
    } on DigitalOnboardingException catch (e) {
      return CustomSnackBar.errorSnackBar(
        message: e.message ?? Strings.SOMETHING_WENT_WRONG,
      );
    }
  }

  void startTimer() {
    update(["resend_otp"]);
    otpTimer = Timer.periodic(
      const Duration(seconds: 1),
      (_) {
        final seconds = myDuration.inSeconds - 1;
        if (seconds == -1) {
          otpTimer?.cancel();
          update(["confirm_otp_button"]);
        } else {
          myDuration = Duration(seconds: seconds);
        }
        update(["otp_timer_text"]);
      },
    );
  }

  String getOtpDuration() {
    String strDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = strDigits(myDuration.inMinutes.remainder(60));
    final seconds = strDigits(myDuration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  void onInit() {
    otpModel =
        Get.arguments != null ? Get.arguments['otpModel'] : SessionOtpModel();

    pinCtrl = TextEditingController();


    startTimer();
    super.onInit();
  }

  @override
  void dispose() {
    pinCtrl.dispose();
    otpTimer?.cancel();
    super.dispose();
  }
}
