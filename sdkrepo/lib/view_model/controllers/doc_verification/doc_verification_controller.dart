import 'package:digital_onboarding/digital_onboarding.dart';

import '../../../resources/exports/index.dart';

enum DocType { front, back }

class DocVerificationController extends GetxController {
  Uint8List? image;
  DocType docType = DocType.front;
  VerifyDocModel? doc;

  Uint8List? doc1;
  Uint8List? doc2;

  void pickImage() async {
    if (docType == DocType.front) {
      image = await ImagePickerService.showAIPickerSheet(Get.context!);
    } else {
      image = await ImagePickerService.openFrame('national');
    }
    Get.back();
    await updateImage();
  }

  Future<void> updateImage() async {
    update(['doc_picture']);
  }

  Future<void> verify() async {
    if (image == null) {
      CustomSnackBar.errorSnackBar(message: Strings.IMAGE_FIRST);
      return;
    }

    try {
      final XFile file =
          await GlobalHelper().getFileFromUint8List(image!, "doc");
      final VerifyDocModel? model =
          await DigitalOnboardingServices.submitDocument(
        file,
        step: docType == DocType.front
            ? StepEnum.documentFront
            : StepEnum.documentBack,
      );

      if (model?.verified ?? false) {
        if (model?.isBackRequired ?? false) {
          docType = DocType.back;
          image = null;
          updateImage();
          CustomSnackBar.successSnackBar(
              message: "Upload Back Side Of Document Now...");
          return;
        }
      } else {
        return CustomSnackBar.errorSnackBar(message: Strings.IMAGE_FIRST);
      }

      Get.back();
      Get.offAllNamed(Routes.DASHBOARD);
    } on DigitalOnboardingException catch (e) {
      CustomSnackBar.errorSnackBar(message: Strings.SOMETHING_WENT_WRONG);
    }
  }

  Future<void> onUpdate() async {
    if (doc1 == null) {
      CustomSnackBar.errorSnackBar(message: Strings.IMAGE_FIRST);
      return;
    }

    if (doc1 != null) {
      try {
        XFile file =
            await GlobalHelper().getFileFromUint8List(doc1!, "Document1");
        await DigitalOnboardingServices.submitDocument(file,
            step: StepEnum.documentFront);
        Get.offAllNamed(Routes.DASHBOARD);
      } on DigitalOnboardingException catch (e) {}
    }

    if (doc2 != null) {
      try {
        XFile file =
            await GlobalHelper().getFileFromUint8List(doc2!, "Document2");
        await DigitalOnboardingServices.submitDocument(file,
            step: StepEnum.documentFront);
        Get.offAllNamed(Routes.DASHBOARD);
      } on DigitalOnboardingException catch (e) {}
    }
  }

  @override
  void dispose() {
    doc1 = null;
    doc2 = null;
    super.dispose();
  }
}
