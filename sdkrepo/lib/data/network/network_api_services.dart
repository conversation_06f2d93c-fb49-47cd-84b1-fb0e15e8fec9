import '../../resources/exports/index.dart';
import 'package:http/http.dart' as http;

import '../exceptions/exceptions.dart';
import 'base_api_services.dart';

class NetworkApiServices extends BaseApiServices {
  Map<String, String> generateHeaders() {
    Map<String, String> headers = {
      'Content-Type': 'application/json',
    };
    return headers;
  }


  @override
  Future postApi(String url,{Map<String, dynamic>? body}) async {
    try {
      http.Response response = await http
          .post(
            Uri.https("auth-onboarding-middleware.rndlabs.dev", url),
            headers: generateHeaders(),
            body: body != null ? jsonEncode(body) : null,
          )
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        dynamic responseJson = jsonDecode(response.body);
        return responseJson["encrypted_data"];
      } else {
       throw Exception();
      }
    } on SocketException { throw Exception();

    } catch (e) {
     throw Exception();
    }
  }



  dynamic validateResponse(http.Response response) {
    switch (response.statusCode) {
      case 400:
        CustomSnackBar.errorSnackBar(message: InvalidUrlException().message);
        throw InvalidUrlException();
      case 403:
        CustomSnackBar.errorSnackBar(message: RestrictedAccess().message);
        throw RestrictedAccess();
      case 401:
        CustomSnackBar.errorSnackBar(message: UnauthorizedException().message);
        throw UnauthorizedException();
      case 305:
        CustomSnackBar.errorSnackBar(message: InvalidTokenException().message);
        throw InvalidTokenException();
      case 408:
        CustomSnackBar.errorSnackBar(message: RequestTimeout().message);
        throw RequestTimeout();
      case 500:
        CustomSnackBar.errorSnackBar(message: ServerException().message);
        throw ServerException();
      default:
        dynamic responseJson = jsonDecode(response.body);
        CustomSnackBar.errorSnackBar(message: responseJson['failMessage']);
        throw UnknownError();
    }
  }
}
