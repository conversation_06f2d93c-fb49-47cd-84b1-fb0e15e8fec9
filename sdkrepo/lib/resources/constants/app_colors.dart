part of 'constants.dart';

class AppColors {
  static const Color primary = Colors.black;
  static const Color primaryBlue = Color(0xFF3DDAF1);
  static const Color primaryLight = Color.fromARGB(255, 94, 69, 112);
  static const Color secondary = Color.fromARGB(255, 112, 1, 192);

  static const Color error = Color(0xFFFF3030);
  static const Color success = Color(0xFF7D9B3D);
  static const Color warning = Color(0xFFEC7C1D);
  static const Color information = Color(0xFF1FA2D4);

  static Color shadow = Colors.black.withOpacity(0.2);
  static Color shadowLight = primaryLight.withOpacity(0.1);
  
  static const Color scaffoldBackground = Color(0xFFEBF2F8);
  static const Color white = Colors.white;
  static const Color black = Colors.black;
  static const Color enableBorder = Color(0xFF99A4A8);
  static const Color hint = Color(0xFF949FA6);
  static const Color disabled = Color(0xFF949FA6);
  static const Color divider = Color(0xFFEFEFEF);
  static const Color secondaryText = Color(0xFFF2F2F2);
  static const Color iconGrey = Color(0xFFE0E0E0);
  static const Color searchIconGrey = Color(0xFF707070);
  static const Color unSelectedAlphabet = Color(0xFFB7B3C7);
  static const Color highlight = Color(0xFFE5E5E5);
  static const Color label = Color(0xFF999797);
  static const Color favorite = Color(0xFFFF5656);
  static const Color fbBlue = Color(0xFF1877F2);
  static const Color linkdinBlue = Color(0xFF0A66C2);
  static const Color twitterBlue = Color(0xFF1DA1F2);
  static const Color closedRed = Color(0xFFFF5252);
  static const Color greyShade2 = Color(0xFF7D7D7D);
  static const Color greyShade3 = Color(0xFFC9C9C9);

  // Rating
  static const Color rating = Color(0xFFF5921E);
  static const Color ratingGrey = Color(0xFFE4E4E4);
  static const Color searchFieldBorder = Color(0xFF6C757D);
}
