// ignore_for_file: constant_identifier_names
part of 'constants.dart';

class Strings {
  static const String APP_NAME = "Demo";
  static const String CACHE_BOX_KEY = 'cacheBoxKey';

  // Fonts Name
  static const String POPPINS = 'Gotham'; //TODO: replace this font to make all change in one place, change it in future time.

// Nested navigation key
  static const int GET_NESTED_KEY_1 = 1;

// Offline Screen
  static const String NO_INTERNET_CONNECTION = 'No Internet Connection. Make sure that Wifi or Mobile data is turned on, then try again.';
  static const String OFFLINE_MESSAGE = 'You are offline. Please connect to internet first .';
  static const String OPEN_DATA_SETTINGS = 'Open Data Settings';
  static const String OPEN_WIFI_SETTINGS = 'Open Wifi Settings';
  static const String YOU_ARE_OFFLINE = "You're Offline.";

// Home
  static const String PRESS_CONTINUE = 'Press Any Button Below To Start Verification';
  static const String CONTINUE = 'Continue';
  static const String BACK = 'Back';
  static const String START_KYC = 'Start Kyc';
  static const String CONTINUE_OLD_KYC = 'Continue Old Kyc';

// Login
  static const String USER_NAME = 'User name';
  static const String password = 'Password';
  static const String EMAIL = 'Email';
  static const String PHONE = 'Phone';
  static const String VERIFY = 'Verify';
  static const String SEND = 'Send';
  static const String COMPLETE_KYC = 'Submit KYC';
  static const String SHOW_SUMMARY = 'View Summary';
  static const String THANK_YOU = 'Thank You';
  static const String SAVE = 'Save';
  static const String PLEASE_ENTER_YOUR_EMAIL = 'Please enter your Email to start verification';
  static const String ENTER_YOUR_USER_NAME = 'Please enter your user name and password to start';
  static const String PLEASE_ENTER_YOUR_PHONE = 'Please enter your Phone to start verification';

  // Dashboard
  static const String VERIFICATION_STEPS = 'Verification Steps';
  static const String Summary = 'Summary';

  static const String VERIFY_BY = 'Verify By Email/Otp';
  static const String SELFIE_VERIFICTION = 'Selfie Verification';
  static const String DOCUMENT_VERIFICATION = 'Document Verification';
  static const String EXTRA_DOCUMENTS = 'Extra documents';
  static const String NFC_VERIFICATION = 'NFC Verification';
  static const String Location_GET = 'Add Your Address';
  static const String Extra_DOCS = 'Add Extra Documents';
  static const String Extra_DOCS_Summary = 'Extra Documents';

  static const String QUESTIONS = 'Questions';
  static const String SIGNATURE = 'Signature';

  // Login
  static const String EMAIL_VRIFICATION = 'Email / Phone Verification';
  static const String LOGIN = 'Login';
  static const String ENTER_OTP = 'Enter Otp';
  static const String OTP_VERIFICATION = 'Otp Verification';
  static const String RESEND = 'Resend';
  static const String ONE_TIME_PASS = "Enter the One-Time Password (OTP) sent to your registered phone/Email to verify your account.";

  // Doc verification
  static const String DO_YOU_WANT = 'Do you want to send your document and image to verify?';
  static const String USE_CAMERA = 'Use camera to verify your document';
  static const String FROM_CAMERA = "Pick Image From Camera";
  static const String FROM_GALLERY = "Pick Image From Gallery";
  static const String FROM_PASSPORT = "Capture Passport";
  static const String FROM_CARD = "Capture National Identity Card";

  // Questions
  static const String ANSWER = 'Answer';
  static const String NEXT = 'Next';
  static const String DONE = 'Done';
  static const String PREVIOUS = 'Previous';
  static const String MIN = 'Min';
  static const String MAX = 'Max';
  static const String ADDRESS = 'Address';

  static const String BUILDING_CREDIT = 'Building credit/loans';
  static const String EVERYDAY_PURCHASES = 'Everyday purchases';
  static const String TRAVEL = 'Travel';
  static const String ONLINE_SHOPPING = 'Online shopping';
  static const String LARGE_PURCHASES = 'Large purchases';
  static const String BUSINESS_EXPENSES = 'Business expenses';
  static const String OTHER = 'Other';

  static const String FULLTIME = 'Full Time';
  static const String CONTRACT = 'Contract';
  static const String PART_TIME = 'Part-time';
  static const String FREELANCE = 'Freelance';
  static const String SELF_EMPLOYED = 'Self-employed';
  static const String INTERN = 'Intern';
  static const String VOLUNTEER = 'Volunteer';
  static const String STUDENT = 'Student';
  static const String UNEMPLOYED = 'Unemployed';

  static const String BELOW_600K = 'Below 600K IQD IQD';
  static const String SIX_NINE = '600K-900K IQD';
  static const String MIL_ONE_HALF_MIL = '1M-1.5M IQD';
  static const String ONE_HALF_MIL_TWO_MIL = '1.5M-2M IQD';
  static const String MIL_TWO_MIL_TOW_HAL_MIL = '2M-2.5M IQD';
  static const String TOW_HALF_MIL_THREE_MIL = '2.5M-3M IQD';
  static const String MIL_THREE_MIL_THREE_EIGHT_MIL = '3M-3.8M IQD';
  static const String FOUR_MIL_SIX_MIL = '4M-6M IQD';
  static const String ABOVE_SIX_MIL = 'Above 6M IQD';

  static const String EMP_CONTACT = 'Employer Contact';

  // Questions
  static const String JOB_TITLE = 'What is your Job Title?';
  static const String EMPLOYMENT_TYPE = 'What is your Employment Type?';
  static const String EMPLOYER_CONTACT = 'What is your Employer Contact?';
  static const String SALARY_RANGE = 'What is your Salary Range/Monthly income average?';
  static const String CONTACT_PREFERENCES = 'Please provide Contact Preferences';
  static const String WHAT_IS_THE_PURPOSE = 'What is the purpose of requesting the card/wallet, etc.';

  // Signature
  static const String CLEAR = 'Clear';
  static const String PLEASE_DRAW = 'Please draw the signature first';

// Snackbar Messages
  static const String RESTRICTED_ACCESS = 'Restricted Access.';
  static const String FILE_IS_EMPTY = 'File is empty';
  static const SOMETHING_WENT_WRONG = 'Something went wrong please try again';
  static const String SUCCESS = 'Success';
  static const String ERROR = 'Error';
  static const String REQUIRED_FIELDS = 'Please fill the required fields';
  static const String NOT_SUPPORTED = 'Operation is not supported on this platform.';
  static const String CLOSE_APPLICATION = 'Press back button again to close application!';
  static const String IMAGE_FIRST = 'Please select image first';
  static const String INVALID_OTP = 'Invalid Otp';
  static const String PLEASE_PROVIDE_IMAGE = 'Please provide image from backside of your document !';
  static const String PLEASE_COMPLETE = 'Please complete all the steps first';
}
