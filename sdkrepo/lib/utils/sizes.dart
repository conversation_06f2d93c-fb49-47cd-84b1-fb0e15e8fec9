// ignore_for_file: constant_identifier_names

class Sizes {
  static const double SIZE_120 = 120.0;
  static const double SIZE_60 = 60.0;
  static const double SIZE_48 = 48.0;
  static const double SIZE_36 = 36.0;
  static const double SIZE_24 = 24.0;
  static const double SIZE_20 = 20.0;
  static const double SIZE_16 = 16.0;
  static const double SIZE_12 = 12.0;
  static const double SIZE_8 = 8.0;
  static const double SIZE_6 = 6.0;
  static const double SIZE_4 = 4.0;
  static const double SIZE_2 = 2.0;
  static const double SIZE_1 = 1.0;
  static const double SIZE_0 = 0.0;

  // TextSizes
  static const double TEXT_SIZE_96 = 96.0;
  static const double TEXT_SIZE_60 = 60.0;
  static const double TEXT_SIZE_50 = 50.0;
  static const double TEXT_SIZE_48 = 48.0;
  static const double TEXT_SIZE_44 = 44.0;
  static const double TEXT_SIZE_40 = 40.0;
  static const double TEXT_SIZE_36 = 36.0;
  static const double TEXT_SIZE_34 = 34.0;
  static const double TEXT_SIZE_32 = 32.0;
  static const double TEXT_SIZE_30 = 30.0;
  static const double TEXT_SIZE_28 = 28.0;
  static const double TEXT_SIZE_24 = 24.0;
  static const double TEXT_SIZE_22 = 22.0;
  static const double TEXT_SIZE_20 = 20.0;
  static const double TEXT_SIZE_18 = 18.0;
  static const double TEXT_SIZE_16 = 16.0;
  static const double TEXT_SIZE_14 = 14.0;
  static const double TEXT_SIZE_12 = 12.0;
  static const double TEXT_SIZE_10 = 10.0;
  static const double TEXT_SIZE_8 = 8.0;

  // IconSizes
  static const double ICON_SIZE_50 = 50.0;
  static const double ICON_SIZE_40 = 40.0;
  static const double ICON_SIZE_32 = 32.0;
  static const double ICON_SIZE_36 = 36.0;
  static const double ICON_SIZE_30 = 30.0;
  static const double ICON_SIZE_26 = 26.0;
  static const double ICON_SIZE_24 = 24.0;
  static const double ICON_SIZE_22 = 22.0;
  static const double ICON_SIZE_20 = 20.0;
  static const double ICON_SIZE_18 = 18.0;
  static const double ICON_SIZE_16 = 16.0;
  static const double ICON_SIZE_14 = 14.0;
  static const double ICON_SIZE_12 = 12.0;
  static const double ICON_SIZE_10 = 10.0;
  static const double ICON_SIZE_8 = 8.0;

  // Heights
  static const double HEIGHT_500 = 500.0;
  static const double HEIGHT_400 = 400.0;
  static const double HEIGHT_300 = 300.0;
  static const double HEIGHT_280 = 280.0;
  static const double HEIGHT_250 = 250.0;
  static const double HEIGHT_240 = 240.0;
  static const double HEIGHT_230 = 230.0;
  static const double HEIGHT_200 = 200.0;
  static const double HEIGHT_180 = 180.0;
  static const double HEIGHT_160 = 160.0;
  static const double HEIGHT_150 = 150.0;
  static const double HEIGHT_130 = 130.0;
  static const double HEIGHT_120 = 120.0;
  static const double HEIGHT_100 = 100.0;
  static const double HEIGHT_80 = 80.0;
  static const double HEIGHT_60 = 60.0;
  static const double HEIGHT_50 = 50.0;
  static const double HEIGHT_48 = 48.0;
  static const double HEIGHT_46 = 46.0;
  static const double HEIGHT_44 = 44.0;
  static const double HEIGHT_40 = 40.0;
  static const double HEIGHT_36 = 36.0;
  static const double HEIGHT_32 = 32.0;
  static const double HEIGHT_30 = 30.0;
  static const double HEIGHT_25 = 25.0;
  static const double HEIGHT_24 = 24.0;
  static const double HEIGHT_22 = 22.0;
  static const double HEIGHT_20 = 20.0;
  static const double HEIGHT_18 = 18.0;
  static const double HEIGHT_16 = 16.0;
  static const double HEIGHT_14 = 14.0;
  static const double HEIGHT_12 = 12.0;
  static const double HEIGHT_10 = 10.0;
  static const double HEIGHT_8 = 8.0;
  static const double HEIGHT_6 = 6.0;
  static const double HEIGHT_4 = 4.0;
  static const double HEIGHT_3 = 3.0;
  static const double HEIGHT_2 = 2.0;
  static const double HEIGHT_1 = 1.0;

  // Widths
  static const double WIDTH_400 = 400.0;
  static const double WIDTH_300 = 300.0;
  static const double WIDTH_280 = 280.0;
  static const double WIDTH_250 = 250.0;
  static const double WIDTH_270 = 270.0;
  static const double WIDTH_236 = 236.0;
  static const double WIDTH_230 = 230.0;
  static const double WIDTH_200 = 200.0;
  static const double WIDTH_180 = 180.0;
  static const double WIDTH_170 = 170.0;
  static const double WIDTH_160 = 160.0;
  static const double WIDTH_150 = 150.0;
  static const double WIDTH_120 = 120.0;
  static const double WIDTH_100 = 100.0;
  static const double WIDTH_80 = 80.0;
  static const double WIDTH_60 = 60.0;
  static const double WIDTH_50 = 50.0;
  static const double WIDTH_40 = 40.0;
  static const double WIDTH_36 = 36.0;
  static const double WIDTH_32 = 32.0;
  static const double WIDTH_30 = 30.0;
  static const double WIDTH_25 = 25.0;
  static const double WIDTH_24 = 24.0;
  static const double WIDTH_22 = 22.0;
  static const double WIDTH_20 = 20.0;
  static const double WIDTH_18 = 18.0;
  static const double WIDTH_16 = 16.0;
  static const double WIDTH_14 = 14.0;
  static const double WIDTH_12 = 12.0;
  static const double WIDTH_10 = 10.0;
  static const double WIDTH_8 = 8.0;
  static const double WIDTH_6 = 6.0;
  static const double WIDTH_4 = 4.0;
  static const double WIDTH_2 = 2.0;
  static const double WIDTH_1 = 1.0;
  static const double WIDTH_0 = 0.0;

  // Margins
  static const double MARGIN_200 = 200.0;
  static const double MARGIN_60 = 60.0;
  static const double MARGIN_48 = 48.0;
  static const double MARGIN_46 = 46.0;
  static const double MARGIN_44 = 44.0;
  static const double MARGIN_40 = 40.0;
  static const double MARGIN_36 = 36.0;
  static const double MARGIN_32 = 32.0;
  static const double MARGIN_30 = 30.0;
  static const double MARGIN_26 = 26.0;
  static const double MARGIN_24 = 24.0;
  static const double MARGIN_22 = 22.0;
  static const double MARGIN_20 = 20.0;
  static const double MARGIN_18 = 18.0;
  static const double MARGIN_16 = 16.0;
  static const double MARGIN_14 = 14.0;
  static const double MARGIN_12 = 12.0;
  static const double MARGIN_10 = 10.0;
  static const double MARGIN_8 = 8.0;
  static const double MARGIN_6 = 6.0;
  static const double MARGIN_4 = 4.0;
  static const double MARGIN_2 = 2.0;
  static const double MARGIN_0 = 0.0;

  // Paddings
  static const double PADDING_100 = 100.0;
  static const double PADDING_90 = 90.0;
  static const double PADDING_80 = 80.0;
  static const double PADDING_70 = 70.0;
  static const double PADDING_60 = 60.0;
  static const double PADDING_50 = 50.0;
  static const double PADDING_40 = 40.0;
  static const double PADDING_36 = 36.0;
  static const double PADDING_32 = 32.0;
  static const double PADDING_28 = 28.0;
  static const double PADDING_24 = 24.0;
  static const double PADDING_22 = 22.0;
  static const double PADDING_20 = 20.0;
  static const double PADDING_18 = 18.0;
  static const double PADDING_16 = 16.0;
  static const double PADDING_14 = 14.0;
  static const double PADDING_12 = 12.0;
  static const double PADDING_10 = 10.0;
  static const double PADDING_8 = 8.0;
  static const double PADDING_6 = 6.0;
  static const double PADDING_4 = 4.0;
  static const double PADDING_2 = 2.0;
  static const double PADDING_0 = 0.0;

  // Radius
  static const double RADIUS_80 = 80.0;
  static const double RADIUS_70 = 70.0;
  static const double RADIUS_60 = 60.0;
  static const double RADIUS_50 = 50.0;
  static const double RADIUS_40 = 40.0;
  static const double RADIUS_32 = 32.0;
  static const double RADIUS_30 = 30.0;
  static const double RADIUS_28 = 28.0;
  static const double RADIUS_26 = 26.0;
  static const double RADIUS_24 = 24.0;
  static const double RADIUS_22 = 22.0;
  static const double RADIUS_20 = 20.0;
  static const double RADIUS_18 = 18.0;
  static const double RADIUS_16 = 16.0;
  static const double RADIUS_14 = 14.0;
  static const double RADIUS_12 = 12.0;
  static const double RADIUS_10 = 10.0;
  static const double RADIUS_8 = 8.0;
  static const double RADIUS_6 = 6.0;
  static const double RADIUS_4 = 4.0;
  static const double RADIUS_2 = 2.0;
  static const double RADIUS_0 = 0.0;

  // Elevations
  static const double ELEVATION_16 = 16.0;
  static const double ELEVATION_14 = 14.0;
  static const double ELEVATION_12 = 12.0;
  static const double ELEVATION_10 = 10.0;
  static const double ELEVATION_8 = 8.0;
  static const double ELEVATION_6 = 6.0;
  static const double ELEVATION_4 = 4.0;
  static const double ELEVATION_2 = 2.0;
  static const double ELEVATION_1 = 1.0;
  static const double ELEVATION_0 = 0.0;
}
