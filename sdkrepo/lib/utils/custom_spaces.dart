import 'package:flutter/material.dart';

class SpaceH2 extends StatelessWidget {
  const SpaceH2({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(height: 2.0);
  }
}

class SpaceH4 extends StatelessWidget {
  const SpaceH4({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(height: 4.0);
  }
}

class SpaceH8 extends StatelessWidget {
  const SpaceH8({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(height: 8.0);
  }
}

class SpaceH12 extends StatelessWidget {
  const SpaceH12({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(height: 12.0);
  }
}

class SpaceH16 extends StatelessWidget {
  const SpaceH16({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(height: 16.0);
  }
}

class SpaceH20 extends StatelessWidget {
  const SpaceH20({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(height: 20.0);
  }
}

class SpaceH24 extends StatelessWidget {
  const SpaceH24({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(height: 24.0);
  }
}

class SpaceH30 extends StatelessWidget {
  const SpaceH30({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(height: 30.0);
  }
}

class SpaceH32 extends StatelessWidget {
  const SpaceH32({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(height: 32.0);
  }
}

class SpaceH36 extends StatelessWidget {
  const SpaceH36({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(height: 36.0);
  }
}

class SpaceH40 extends StatelessWidget {
  const SpaceH40({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(height: 40.0);
  }
}

class SpaceH44 extends StatelessWidget {
  const SpaceH44({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(height: 44.0);
  }
}

class SpaceH48 extends StatelessWidget {
  const SpaceH48({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(height: 48.0);
  }
}

class SpaceH60 extends StatelessWidget {
  const SpaceH60({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(height: 60.0);
  }
}

class SpaceH80 extends StatelessWidget {
  const SpaceH80({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(
      height: 80.0,
    );
  }
}

class SpaceH96 extends StatelessWidget {
  const SpaceH96({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(
      height: 96.0,
    );
  }
}

class SpaceH120 extends StatelessWidget {
  const SpaceH120({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(
      height: 120.0,
    );
  }
}

class SpaceH180 extends StatelessWidget {
  const SpaceH180({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(height: 180.0);
  }
}

class SpaceH200 extends StatelessWidget {
  const SpaceH200({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(height: 200.0);
  }
}

// Widths

class SpaceW2 extends StatelessWidget {
  const SpaceW2({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(width: 2.0);
  }
}

class SpaceW4 extends StatelessWidget {
  const SpaceW4({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(width: 4.0);
  }
}

class SpaceW8 extends StatelessWidget {
  const SpaceW8({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(width: 8.0);
  }
}

class SpaceW12 extends StatelessWidget {
  const SpaceW12({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(width: 12.0);
  }
}

class SpaceW16 extends StatelessWidget {
  const SpaceW16({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(width: 16.0);
  }
}

class SpaceW20 extends StatelessWidget {
  const SpaceW20({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(width: 20.0);
  }
}

class SpaceW24 extends StatelessWidget {
  const SpaceW24({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(width: 24.0);
  }
}

class SpaceW30 extends StatelessWidget {
  const SpaceW30({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(width: 30.0);
  }
}

class SpaceW36 extends StatelessWidget {
  const SpaceW36({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(width: 36.0);
  }
}

class SpaceW40 extends StatelessWidget {
  const SpaceW40({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(width: 40.0);
  }
}

class SpaceW48 extends StatelessWidget {
  const SpaceW48({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(width: 48.0);
  }
}

class SpaceW60 extends StatelessWidget {
  const SpaceW60({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(width: 60.0);
  }
}
