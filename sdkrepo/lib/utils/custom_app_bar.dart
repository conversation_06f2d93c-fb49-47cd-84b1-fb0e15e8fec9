import '../resources/exports/index.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final Widget? actions;
  final VoidCallback? onTap;
  final SystemUiOverlayStyle overlayStyle;
  final Color backgroundColor;
  final bool backallow;

  const CustomAppBar({
    super.key,
    required this.title,
    this.actions,
    this.onTap,
    this.backallow=false,
    this.overlayStyle = SystemUiOverlayStyle.dark,
    this.backgroundColor = AppColors.white,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      automaticallyImplyLeading: false,
      backgroundColor: backgroundColor,
      systemOverlayStyle: overlayStyle,
      scrolledUnderElevation: 0.0,
      title: Text(title),
      centerTitle: true,
      leadingWidth: 80.0,
      leading: backallow?GestureDetector(
        onTap: Get.back,
        child: const Icon(Icons.arrow_back_ios, color: AppColors.black),
      ):null,
      actions: [
        if (actions != null) ...[actions!],
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
