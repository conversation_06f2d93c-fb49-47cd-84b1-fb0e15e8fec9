class QAModel {
  String? jobTitle;
  String? employmentType;
  String? employerContact;
  String? purposeOfRequesting;
  String? salaryRange;
  ContactPreferencesDetails? contactPreferences;

  QAModel({
    this.jobTitle,
    this.employmentType,
    this.employerContact,
    this.salaryRange,
    this.contactPreferences,
    this.purposeOfRequesting,
  });

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'jobTitle': jobTitle,
      'employmentType': employmentType,
      'employerContact': employerContact,
      'salaryRange': salaryRange,
      'purposeOfRequesting': purposeOfRequesting,
      'contactPreferences': contactPreferences?.toJson(),
    };
  }

  factory QAModel.fromJson(Map<String, dynamic> json) {
    return QAModel(
      jobTitle: json['jobTitle'] as String?,
      employmentType: json['employmentType'] as String?,
      employerContact: json['employerContact'] as String?,
      purposeOfRequesting: json['purposeOfRequesting'] as String?,
      salaryRange: json['salaryRange'] as String?,
      contactPreferences: json['contactPreferences'] != null
          ? ContactPreferencesDetails.fromJson(json['contactPreferences'])
          : null,
    );
  }

  QAModel copyWith({
    String? jobTitle,
    String? employmentType,
    String? employerContact,
    String? purposeOfRequesting,
    String? salaryRange,
    ContactPreferencesDetails? contactPreferences,
  }) {
    return QAModel(
      jobTitle: jobTitle ?? this.jobTitle,
      employmentType: employmentType ?? this.employmentType,
      employerContact: employerContact ?? this.employerContact,
      salaryRange: salaryRange ?? this.salaryRange,
      purposeOfRequesting: purposeOfRequesting ?? this.purposeOfRequesting,
      contactPreferences: contactPreferences ?? this.contactPreferences,
    );
  }
}

class ContactPreferencesDetails {
  String? email;
  String? address;

  ContactPreferencesDetails({this.email, this.address});

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'email': email,
      'address': address,
    };
  }

  factory ContactPreferencesDetails.fromJson(Map<String, dynamic> json) {
    return ContactPreferencesDetails(
      email: json['email'] as String?,
      address: json['address'] as String?,
    );
  }

  ContactPreferencesDetails copyWith({
    String? email,
    String? address,
  }) {
    return ContactPreferencesDetails(
      email: email ?? this.email,
      address: address ?? this.address,
    );
  }
}
