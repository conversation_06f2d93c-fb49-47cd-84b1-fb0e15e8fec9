class SessionOtpModel {
  int? otp;
  String? email;
  String? phone;

  SessionOtpModel({this.otp, this.email, this.phone});

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'otp': otp,
      'email': email,
      'phone': phone,
    };
  }

  factory SessionOtpModel.fromJson(Map<String, dynamic> json) {
    return SessionOtpModel(
      otp: json['otp'] as int?,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
    );
  }
}
