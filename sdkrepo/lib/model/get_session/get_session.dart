class GetSessionModel {
  bool? login;
  bool? selfie;
  bool? document;
  bool? nfc;
  bool? question;
  bool? signature;
  bool? locationdata;
  bool? extradocuments;

  GetSessionModel({
    this.login,
    this.selfie,
    this.document,
    this.nfc,
    this.question,
    this.signature,
    this.locationdata,
    this.extradocuments,
  });

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'login': login,
      'selfie': selfie,
      'document': document,
      'nfc': nfc,
      'question': question,
      'signature': signature,
      'locationdata': locationdata,
      'extradocuments': extradocuments,
    };
  }

  factory GetSessionModel.fromJson(Map<String, dynamic> json) {
    return GetSessionModel(
      login: json['login'] as bool?,
      selfie: json['selfie'] as bool?,
      document: json['document'] as bool?,
      nfc: json['nfc'] as bool?,
      question: json['question'] as bool?,
      signature: json['signature'] as bool?,
      locationdata: json['locationdata'] as bool?,
      extradocuments: json['extradocuments'] as bool?,
    );
  }
}
