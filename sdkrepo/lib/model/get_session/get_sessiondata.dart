class GetSessionDataModel {
  String? sessionid;
  String? logintype;
  String? emailorphone;
  String? selfie;
  String? nfc;
  String? nfcimage;
  String? signature;
  String? questions;
  List<dynamic>? document;
  List<dynamic>? extradocumets;
  AddressDetails? locationdata;


  GetSessionDataModel({
    this.sessionid,
    this.logintype,
    this.emailorphone,
    this.selfie,
    this.nfc,
    this.nfcimage,
    this.signature,
    this.questions,
    this.document,
    this.extradocumets,
    this.locationdata
  });

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'sessionid': sessionid,
      'logintype': logintype,
      'emailorphone': emailorphone,
      'selfie': selfie,
      'nfc': nfc,
      'nfcimage': nfcimage,
      'signature':signature,
      'questions':questions,
      'document':document,
      'extradocumets':extradocumets,
      'locationdata':locationdata?.toJson()
    };
  }

  factory GetSessionDataModel.fromJson(Map<String, dynamic> json) {
    return GetSessionDataModel(
      sessionid: json['session_id'] as String?,
      logintype: json['login_type'] as String?,
      emailorphone: json['email_or_phone'] as String?,
      selfie: json['selfie'] as String?,
      nfc: json['nfc'] as String?,
      nfcimage: json['nfc_image'] as String?,
      signature: json['signature'] as String?,
      questions: json['questions'] as String?,
      document: json['document'] as List<dynamic>?,
      extradocumets: json['extra_documents'] as List<dynamic>?,
      locationdata: AddressDetails.fromJson(json['address_data'])
    );
  }
}

class AddressDetails {
  String? address;
  String? lat;
  String? lng;

  AddressDetails({this.address, this.lat, this.lng});

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'address': address,
      'lat': lat,
      'lng':lng
    };
  }

  factory AddressDetails.fromJson(Map<String, dynamic> json) {
    return AddressDetails(
      address: json['address'] as String?,
      lat: json['lat'] as String?,
      lng: json['lng'] as String?
    );
  }

}
